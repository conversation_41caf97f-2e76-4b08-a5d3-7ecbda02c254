<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="商品名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入商品名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="商品标识" prop="slug">
        <el-input v-model="queryParams.slug" placeholder="请输入商品标识符" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="价格区间" prop="priceRange">
        <div class="price-range-container">
          <el-input
            v-model="queryParams.minPrice"
            placeholder="最低价格"
            clearable
            @keyup.enter="handleQuery"
            @change="validatePriceRange"
            style="width: 120px;"
            type="number"
            :min="0"
            :precision="2"
          />
          <span class="price-separator">-</span>
          <el-input
            v-model="queryParams.maxPrice"
            placeholder="最高价格"
            clearable
            @keyup.enter="handleQuery"
            @change="validatePriceRange"
            style="width: 120px;"
            type="number"
            :min="0"
            :precision="2"
          />
          <span class="price-unit">¥</span>
          <el-button
            v-if="queryParams.minPrice || queryParams.maxPrice"
            size="small"
            type="info"
            text
            @click="clearPriceRange"
            style="margin-left: 8px;"
          >
            清除
          </el-button>
        </div>
      </el-form-item>
      <el-form-item label="所属商家" prop="merchant">
        <el-input v-model="queryParams.merchant" placeholder="请输入所属商家" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="所属平台" prop="platform">
        <el-select v-model="queryParams.platform" style="width: 200px" placeholder="请选择所属平台" clearable>
          <el-option label="淘宝" value="taobao" />
          <el-option label="微店" value="weidian" />
          <el-option label="1688" value="1688" />
        </el-select>
      </el-form-item>
      <el-form-item label="商品状态" prop="status">
        <el-select v-model="queryParams.status" style="width: 200px" placeholder="请选择商品状态" clearable>
          <el-option v-for="dict in products_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="推荐状态" prop="productStatus">
        <el-select v-model="queryParams.productStatus" style="width: 200px" placeholder="请选择推荐状态" clearable>
          <el-option label="普通商品" :value="0" />
          <el-option label="热销" :value="1" />
          <el-option label="推荐" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:OmgProducts:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:OmgProducts:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:OmgProducts:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:OmgProducts:export']">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Plus" @click="showSummerDialog = true">
          设置当季新品
          <span v-if="summerProductsPreloading" style="margin-left: 5px; color: #409EFF; font-size: 12px;">
            (加载中{{ summerProductsPreloadProgress.current }}/{{ summerProductsPreloadProgress.total }})
          </span>
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleImport"
      
        >批量导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Download"
          @click="importTemplate"
         
        >下载模板</el-button>
      </el-col>
      <!-- 汇率相关按钮已注释 -->
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Setting"
          @click="showExchangeRateDialog = true"
        >设置汇率</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Refresh"
          @click="fetchRealTimeExchangeRate"
          :loading="fetchingRate"
        >获取实时汇率</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Upload"
          @click="handleBatchUpdateQC"
          :loading="batchUpdatingQC"
        >获取/更新所有QC</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Refresh"
          @click="handleSyncAllProducts"
          :loading="syncingProducts"
        >全量同步重量/运输天数</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Connection"
          @click="handleSyncSelectedProducts"
          :loading="syncingSelectedProducts"
          :disabled="multiple"
        >同步重量/运输天数选中商品</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Picture"
          @click="handleUpdateAllMainImages"
          :loading="updatingMainImages"
        >更新主图库</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Monitor"
          @click="handleGoToStatusCheck"
          v-hasPermi="['system:productStatusCheck:query']"
        >商品状态检查</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="OmgProductsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" type="index" v-if="columns[0].visible" :show-overflow-tooltip="true" />
      <el-table-column label="商品名称" align="center" prop="name" v-if="columns[1].visible"
        :show-overflow-tooltip="true" />
      <el-table-column label="商品标识" align="center" prop="slug" v-if="columns[2].visible"
        :show-overflow-tooltip="true" />
      <el-table-column label="商品描述" align="center" prop="description" v-if="columns[3].visible"
        :show-overflow-tooltip="true" />
      <!-- <el-table-column label="商品二级分类" align="center" prop="omgBrand.name" :show-overflow-tooltip="true" />
      <el-table-column label="品牌logo" align="center" prop="omgBrand.logoUrl" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.omgBrand.logoUrl" :width="50" :height="50" v-if="scope.row.omgBrand" />
        </template>
      </el-table-column> -->
      <el-table-column label="商品价格" align="center" prop="price" v-if="columns[4].visible"
        :show-overflow-tooltip="true">
        <template #default="scope">
          <span>¥{{ usdToRmb(scope.row.price) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="商品原价" align="center" prop="originalPrice" v-if="columns[5].visible"
        :show-overflow-tooltip="true">
        <template #default="scope">
          <span>¥{{ usdToRmb(scope.row.originalPrice) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="商品主图" align="center" prop="mainImage" width="100" v-if="columns[6].visible">
        <template #default="scope">
          <image-preview :src="scope.row.mainImage" :width="50" :height="50" />
        </template>
      </el-table-column>
      <el-table-column label="商品SKU" align="center" prop="sku" v-if="columns[7].visible"
        :show-overflow-tooltip="true" />
      <el-table-column label="QC" align="center" prop="qc" v-if="columns[8].visible"
        :show-overflow-tooltip="true" />
      <el-table-column label="重量(g)" align="center" prop="weight" v-if="columns[9].visible"
        :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ scope.row.weight ? parseFloat(scope.row.weight).toFixed(2) : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="运输天数" align="center" prop="averageArrival" v-if="columns[10].visible"
        :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ scope.row.averageArrival ? scope.row.averageArrival + '天' : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="点赞数" align="center" prop="likes" v-if="columns[11].visible"
        :show-overflow-tooltip="true" />
      <el-table-column label="浏览量" align="center" prop="views" v-if="columns[12].visible"
        :show-overflow-tooltip="true" />
      <el-table-column label="虚拟浏览量" align="center" prop="virtualViews" v-if="columns[13].visible"
        :show-overflow-tooltip="true" />
      <el-table-column label="所属商家" align="center" prop="merchant" v-if="columns[14].visible"
        :show-overflow-tooltip="true" />
      <el-table-column label="所属平台" align="center" prop="platform" v-if="columns[15].visible"
        :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ getPlatformText(scope.row.platform) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="商品状态" align="center" prop="status" v-if="columns[16].visible">
        <template #default="scope">
          <div class="status-tag">
            <span class="status-dot" :class="getStatusClass(scope.row.status)"></span>
            <span>{{ getStatusText(scope.row.status) }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createdAt" width="180" v-if="columns[17].visible"
        :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标签" align="center" prop="tag" v-if="columns[18].visible">
        <template #default="scope">
          <div class="tag-container">
                          <el-tag
              v-for="(tag, index) in parseTags(scope.row.tag)"
              :key="index"
              :type="tagTypes[index % tagTypes.length]"
              size="small"
            >
              {{ tag }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="来源链接" align="center" prop="fromUrl" v-if="columns[19].visible"
        :show-overflow-tooltip="true">
        <template #default="scope">
          <el-link :href="scope.row.fromUrl" type="primary" :underline="false" target="_blank" v-if="scope.row.fromUrl">
            查看链接
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="视频" align="center" prop="video" v-if="columns[21].visible">
        <template #default="scope">
          <el-button v-if="scope.row.video" type="primary" link icon="VideoPlay" @click="previewVideo(scope.row.video)">预览视频</el-button>
          <span v-else>无视频</span>
        </template>
      </el-table-column>
      <el-table-column label="推荐状态" align="center" prop="productStatus" v-if="columns[20].visible">
        <template #default="scope">
          <el-tag :type="getProductStatusTagType(scope.row.productStatus)" size="small">
            {{ getProductStatusText(scope.row.productStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="商品属性" align="center" prop="productAttributes" v-if="columns[22].visible" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="320">      
        <template #default="scope">
          <div class="operation-buttons">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:OmgProducts:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:OmgProducts:remove']">删除</el-button>
          <el-button link type="success" v-if="scope.row.status === 'inactive'"
            @click="handleActive(scope.row)">上架</el-button>
          <el-button link type="warning" v-else-if="scope.row.status === 'active'"
            @click="handleInactive(scope.row)">下架</el-button>
          <el-button link type="info" icon="Picture" @click="handleViewMainImages(scope.row)">查看所有主图</el-button>
          <el-button
            link
            type="warning"
            icon="Monitor"
            @click="handleCheckProductStatus(scope.row)"
            :loading="scope.row.checking"
            :disabled="!scope.row.sku || !scope.row.platform"
            v-hasPermi="['system:productStatusCheck:single']"
          >
            检测状态
          </el-button>
          <el-dropdown @command="(command) => handleSetProductStatus(scope.row, command)">
            <el-button link type="primary">设置状态<el-icon class="el-icon--right"><arrow-down /></el-icon></el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="0" :disabled="scope.row.productStatus === 0">设为普通</el-dropdown-item>
                <el-dropdown-item :command="1" :disabled="scope.row.productStatus === 1">设为热销</el-dropdown-item>
                <el-dropdown-item :command="2" :disabled="scope.row.productStatus === 2">设为推荐</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改omg_商品对话框 -->
    <el-dialog :title="title" v-model="open" width="550px" append-to-body>
      <el-form ref="OmgProductsRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="商品标识" prop="slug">
          <el-input v-model="form.slug" placeholder="请输入商品标识符" />
        </el-form-item>
        <el-form-item label="商品描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="商品价格" prop="price">
          <el-input v-model="form.price" placeholder="请输入商品价格" />
          <!-- 汇率提示已注释 -->
          <!-- <div class="price-tip">
            <small>注意：请输入人民币价格，系统将自动转换为美元存储（汇率: 1 USD = {{ exchangeRate }} RMB）</small>
          </div> -->
        </el-form-item>
        <el-form-item label="商品原价" prop="originalPrice">
          <el-input v-model="form.originalPrice" placeholder="请输入商品原价" />
          <!-- 汇率提示已注释 -->
          <!-- <div class="price-tip">
            <small>注意：请输入人民币价格，系统将自动转换为美元存储（汇率: 1 USD = {{ exchangeRate }} RMB）</small>
          </div> -->
        </el-form-item>
        <el-form-item label="商品主图" prop="mainImage">
          <image-upload v-model="form.mainImage" :limit="10" multiple />
        </el-form-item>
        <el-form-item label="商品QC图" prop="qcImages">
          <image-upload v-model="form.qcImages" :limit="10" />
        </el-form-item>
        <el-form-item label="商品SKU" prop="sku">
          <el-input v-model="form.sku" placeholder="请输入商品SKU编号" />
        </el-form-item>
        <el-form-item label="商品库存" prop="stock">
          <el-input v-model="form.stock" placeholder="请输入商品库存" />
        </el-form-item>
        <el-form-item label="QC值" prop="qc">
          <el-input v-model="form.qc" placeholder="请输入商品QC值" />
        </el-form-item>
        <el-form-item label="重量(g)" prop="weight">
          <el-input-number v-model="form.weight" :precision="2" :step="0.01" :min="0" placeholder="请输入商品重量(克)" style="width: 100%" />
        </el-form-item>
        <el-form-item label="运输天数" prop="averageArrival">
          <el-input-number v-model="form.averageArrival" :precision="0" :step="1" :min="0" placeholder="请输入平均运输天数" style="width: 100%" />
        </el-form-item>
        <el-form-item label="点赞数" prop="likes">
          <el-input v-model="form.likes" placeholder="请输入商品点赞数" />
        </el-form-item>
        <el-form-item label="浏览量" prop="views">
          <el-input v-model="form.views" placeholder="请输入商品浏览量" />
        </el-form-item>
        <el-form-item label="虚拟浏览量" prop="virtualViews">
          <el-input v-model="form.virtualViews" placeholder="请输入虚拟浏览量" />
        </el-form-item>
        <el-form-item label="商品评分" prop="rating">
          <el-input v-model="form.rating" placeholder="请输入商品评分" />
        </el-form-item>
        <el-form-item label="评分总数" prop="totalRatings">
          <el-input v-model="form.totalRatings" placeholder="请输入评分总数" />
        </el-form-item>
        <el-form-item label="所属商家" prop="merchant">
          <el-input v-model="form.merchant" placeholder="请输入所属商家" />
        </el-form-item>
        <el-form-item label="所属平台" prop="platform">
          <el-select v-model="form.platform" placeholder="请选择所属平台">
            <el-option label="淘宝" value="taobao" />
            <el-option label="微店" value="weidian" />
            <el-option label="1688" value="1688" />
          </el-select>
        </el-form-item>
        <el-form-item label="商品标签" prop="tag">
          <div class="tag-input-container">
            <el-select
              v-model="tagInputValue"
              class="tag-input"
              placeholder="输入标签后按Enter添加"
              :allow-create="true"
              filterable
              multiple
              default-first-option
              @change="handleTagChange"
            >
              <el-option
                v-for="item in tagOptions"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
            <el-button type="primary" icon="Edit" size="small" @click="showTagManageDialog = true">管理标签</el-button>
          </div>
        </el-form-item>
        <el-form-item label="来源链接" prop="fromUrl">
          <el-input v-model="form.fromUrl" placeholder="请输入商品来源链接" />
        </el-form-item>
        <el-form-item label="商品视频" prop="video">
          <media-upload v-model="form.video" :limit="1" />
        </el-form-item>
        <el-form-item label="商品属性" prop="productAttributes">
          <el-select v-model="form.productAttributes" placeholder="请选择商品属性" clearable style="width: 100%">
            <el-option label="rep" value="rep" />
            <el-option label="sp" value="sp" />
          </el-select>
        </el-form-item>
        <el-form-item label="推荐状态" prop="productStatus">
          <el-select v-model="form.productStatus" placeholder="请选择推荐状态">
            <el-option label="普通商品" :value="0" />
            <el-option label="热销" :value="1" />
            <el-option label="推荐" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="商品状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择商品状态">
            <el-option v-for="dict in products_status" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品分类" prop="categoryId">
          <el-select 
            v-model="form.categoryId" 
            placeholder="请选择商品分类"
            :loading="categoryLoading"
            @change="handleCategoryChange"
          >
            <el-option
              v-for="category in categoryOptions"
              :key="category?.categoryId"
              :label="category?.name"
              :value="category?.categoryId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属二级分类" prop="brandId">
          <el-select 
            v-model="form.brandId" 
            placeholder="请选择商品分类"
            :loading="brandLoading"
            @change="handleBrandChange"
          >
            <el-option
              v-for="brand in brandOptions"
              :key="brand?.brandId"
              :label="brand?.name"
              :value="brand?.brandId"
            />
          </el-select>
        </el-form-item>
        
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 夏季新品弹窗 -->
    <el-dialog v-model="showSummerDialog" title="设置夏季新品" width="900px" append-to-body>
      <el-tabs v-model="summerActiveTab">
        <el-tab-pane label="已选商品" name="selected">
          <el-button type="danger" size="small" @click="handleClearSummerProducts" style="margin-bottom: 10px;">清空</el-button>
          <div v-if="summerProductsList.length > 0" class="summer-products-list">
            <div v-for="item in summerProductsList" :key="item.id" class="summer-product-item">
              <img :src="item.imageUrl" alt="商品图片" style="width: 80px; height: 80px; object-fit: contain; border-radius: 4px;" />
              <div class="product-details">
                <div class="product-name">{{ getProductDisplayName(item) }}</div>
                <div class="product-price">￥{{ item.price }}</div>
                <div>浏览: {{ item.views || 0 }} | 点赞: {{ item.likes || 0 }}</div>
                <div>评论: {{ item.comments || 0 }} | 收藏: {{ item.favorites || 0 }}</div>
              </div>
            </div>
          </div>
          <el-empty v-else description="暂无夏季新品数据"></el-empty>
        </el-tab-pane>
        <el-tab-pane label="添加新品" name="add">
          <div style="display: flex; align-items: flex-start; gap: 30px;">
            <div style="flex: 1;">
              <div style="margin-bottom: 10px; font-weight: bold;">{{ selectHintText }}</div>
              <el-select
                v-model="summerSelectedIds"
                multiple
                :multiple-limit="remainingSelectLimit"
                :disabled="remainingSelectLimit === 0"
                filterable
                :placeholder="remainingSelectLimit === 0 ? '已达到最大选择数量' : '请选择商品'"
                style="width: 100%;"
                @change="handleSummerSelectChange"
                @visible-change="handleSelectVisibleChange"
                popper-class="summer-product-select-popper"
              >
                <el-option v-for="item in summerProductOptions" :key="item.productId" :label="getProductDisplayName(item)" :value="item.productId" />
                <div v-if="summerProductsLoading" style="text-align: center; padding: 10px; color: #999;">
                  <i class="el-icon-loading"></i> 正在加载更多商品...
                </div>
                <div v-if="!summerProductsHasMore && summerProductOptions.length > 0" style="text-align: center; padding: 10px; color: #67C23A;">
                  ✅ 已加载全部商品（共{{ summerProductOptions.length }}个）
                </div>
                <div v-if="summerProductOptions.length === 0 && !summerProductsLoading" style="text-align: center; padding: 10px; color: #999;">
                  暂无商品数据
                </div>
                <div v-if="summerProductOptions.length > 0 && summerProductsHasMore && !summerProductsLoading" style="text-align: center; padding: 5px; color: #409EFF; font-size: 12px;">
                  📄 向下滚动加载更多...
                </div>
              </el-select>
            </div>
            <div style="flex: 2;">
              <div style="margin-bottom: 10px; font-weight: bold;">商品信息</div>
              <div v-if="summerSelectedProducts.length" class="selected-products-list">
                <div v-for="item in summerSelectedProducts" :key="item.productId" class="selected-product-item">
                  <img :src="item.mainImage || item.imageUrl" alt="商品图片" style="width: 80px; height: 80px; object-fit: cover; border-radius: 4px; background: #f8f8f8;" />
                  <div class="product-details">
                    <div class="product-name">{{ getProductDisplayName(item) }}</div>
                    <div class="product-price">￥{{ item.price }}</div>
                    <div>库存：{{ item.stock }}</div>
                    <div>状态：{{ getStatusText(item.status) || item.status }}</div>
                  </div>
                </div>
              </div>
              <div v-else style="color: #bbb;">请选择商品</div>
            </div>
          </div>
          <div class="dialog-footer" style="margin-top: 20px; text-align: right;">
            <el-button type="primary" @click="handleAddOrUpdateSummerProducts" :disabled="!summerSelectedProducts.length">{{ isSummerSelectionChanged ? '新增' : '添加所选商品' }}</el-button>
          </div>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <el-button @click="showSummerDialog = false">关闭</el-button>
      </template>
    </el-dialog>





    <!-- 批量导入对话框 -->
    <el-dialog v-model="importOpen" title="批量导入商品" width="500px" append-to-body>
      <div class="import-tips">
        <p class="import-warning">导入注意事项：</p>
        <ul>
          <li>请不要在Excel中填写product_id列，系统会自动生成ID</li>
          <li>请确保slug(商品标识符)在系统中唯一</li>
          <li>价格、原价等数值字段请使用有效数字</li>
          <li>商品状态请填写"active"或"inactive"</li>
        </ul>
        <el-button type="primary" link @click="importTemplate">
          <el-icon><Download /></el-icon>下载导入模板
        </el-button>
      </div>
      <el-upload
        class="upload-demo"
        drag
        action="#"
        :auto-upload="false"
        :on-change="handleFileChange"
        :limit="1"
        accept=".xlsx, .xls"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          拖拽文件到此处，或 <em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传 Excel 文件 (xlsx, xls)
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitImport" :disabled="!uploadFile">确 定</el-button>
          <el-button @click="cancelImport">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 设置汇率对话框已注释 -->
    <!-- <el-dialog v-model="showExchangeRateDialog" title="汇率设置" width="500px" append-to-body>
      <el-form label-width="120px">
        <el-form-item label="实时汇率">
          <div class="realtime-rate-section">
            <el-button
              type="primary"
              @click="fetchRealTimeExchangeRate"
              :loading="fetchingRate"
              size="small"
            >
              {{ fetchingRate ? '获取中...' : '获取今日实时汇率' }}
            </el-button>
            <div v-if="lastFetchTime" class="rate-info">
              <p><strong>最新汇率：</strong>1 USD = {{ exchangeRate }} CNY</p>
              <p><small>更新时间：{{ lastFetchTime }}</small></p>
              <p><small class="rate-source">数据来源：{{rateSource}}</small></p>
            </div>
          </div>
        </el-form-item>
        <el-divider>或手动设置汇率</el-divider>
        <el-form-item label="手动设置汇率">
          <el-input-number
            v-model="tempExchangeRate"
            :precision="2"
            :step="0.01"
            :min="0.01"
            placeholder="请输入汇率"
            style="width: 100%"
          />
          <div class="exchange-rate-tip">
            <small>1 美元 = {{ tempExchangeRate }} 人民币</small>
          </div>
        </el-form-item>
        <el-form-item label="汇率说明">
          <div class="exchange-rate-desc">
            <p>• 优先建议使用"获取今日实时汇率"功能</p>
            <p>• 用户在界面上看到和输入的价格都是人民币</p>
            <p>• 系统会自动将人民币转换为美元存储到数据库</p>
            <p>• 修改汇率后，新的价格转换将使用新汇率</p>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="saveExchangeRate">确 定</el-button>
          <el-button @click="cancelExchangeRate">取 消</el-button>
        </div>
      </template>
    </el-dialog> -->

          <!-- 标签管理对话框 -->
      <el-dialog v-model="showTagManageDialog" title="标签管理" width="600px" append-to-body>
        <el-form :model="tagQueryParams" ref="tagQueryRef" :inline="true" label-width="80px">
        <el-form-item label="标签名称" prop="tagName">
          <el-input v-model="tagQueryParams.tagName" placeholder="请输入标签名称" clearable @keyup.enter="handleTagQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleTagQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetTagQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="tagLoading" :data="tagList" border style="width: 100%">
        <el-table-column type="index" label="序号" width="50" align="center" />
        <el-table-column prop="tagName" label="标签名称" align="center" />
        <el-table-column prop="createdAt" label="创建时间" align="center" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150">
          <template #default="scope">
            <el-button type="primary" size="small" @click="openEditTag(scope.row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDeleteTag(scope.row.tagId)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="tagTotal > 0" :total="tagTotal" v-model:page="tagQueryParams.pageNum"
        v-model:limit="tagQueryParams.pageSize" @pagination="getTagList" />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleAddTag">新增标签</el-button>
          <el-button @click="showTagManageDialog = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑标签对话框 -->
    <el-dialog v-model="showEditTagDialog" title="编辑标签" width="400px" append-to-body>
      <el-form :model="editTagForm" label-width="80px">
        <el-form-item label="标签名称" prop="tagName">
          <el-input v-model="editTagForm.tagName" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditTagDialog = false">取消</el-button>
        <el-button type="primary" @click="submitEditTag">保存</el-button>
      </template>
    </el-dialog>

    <!-- 查看所有主图对话框 -->
    <el-dialog v-model="showMainImagesDialog" title="查看所有主图" width="800px" append-to-body>
      <div v-loading="loadingMainImages">
        <div v-if="mainImagesList.length > 0" class="main-images-grid">
          <div v-for="(image, index) in mainImagesList" :key="image.id" class="image-item">
            <div class="image-wrapper">
              <el-image
                :src="image.ossImageUrl"
                :alt="image.imageName"
                fit="cover"
                class="thumbnail-image"
                :preview-src-list="previewImageList"
                :initial-index="index"
                preview-teleported
              />
              <div class="image-info">
                <div class="image-name">{{ image.imageName }}</div>
                <div class="image-details">
                  <el-tag v-if="image.isMain === 1" type="success" size="small">主图</el-tag>
                  <el-tag v-else type="info" size="small">第{{ image.displayOrder }}张</el-tag>
                  <span class="image-type">{{ image.imageType?.toUpperCase() }}</span>
                </div>
                <div class="image-actions">
                  <el-button
                    v-if="image.isMain !== 1"
                    type="primary"
                    size="small"
                    @click="handleSetMainImage(image)"
                    :loading="settingMainImage"
                  >
                    设为主图
                  </el-button>
                  <div v-else class="main-image-actions">
                    <el-tag type="success" size="small">当前主图</el-tag>
                    <el-button
                      type="warning"
                      size="small"
                      @click="handleCancelMainImage(image)"
                      :loading="cancelingMainImage"
                      style="margin-left: 8px;"
                    >
                      取消主图
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <el-empty v-else description="暂无主图数据" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showMainImagesDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 视频预览功能现在使用Element Plus的MessageBox组件 -->
  </div>
</template>

<style scoped>
.main-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 16px 0;
}

.image-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.image-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.image-wrapper {
  position: relative;
}

.thumbnail-image {
  width: 100%;
  height: 150px;
  cursor: pointer;
}

.image-info {
  padding: 12px;
  background: #fff;
}

.image-name {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
  word-break: break-all;
  line-height: 1.4;
}

.image-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-type {
  font-size: 11px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.operation-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}
</style>

<script setup name="OmgProducts">
import { listOmgProducts, getOmgProducts, delOmgProducts, addOmgProducts, updateOmgProducts, updateProductStatus, batchUpdateAllQC, syncAllProductsWeightAndShipping, updateAllProductMainImages, getProductMainImagesBySku, setProductMainImage, cancelProductMainImage, syncProductsBySkus,getAllOmgProducts } from "@/api/system/OmgProducts";
import { listOmgBrands,getBrandListByCategoryId } from "@/api/system/OmgBrands";
import { listOmgCategories } from "@/api/system/OmgCategories";
import { addOmgSummerProducts, listOmgSummerProducts, updateOmgSummerProducts, delOmgSummerProducts } from "@/api/system/OmgSummerProducts";
import { UploadFilled, Download, ArrowDown, VideoPlay, Picture, Monitor } from '@element-plus/icons-vue';
import { importOmgProducts } from "@/api/system/OmgProducts";
import { checkSingleProductStatus } from "@/api/system/productStatusCheck";
import { onMounted, h, nextTick } from 'vue';

const { proxy } = getCurrentInstance();
const { products_status } = proxy.useDict('products_status');

// 汇率设置相关变量已注释
// const exchangeRate = ref(parseFloat(localStorage.getItem('omg_exchange_rate')) || 7.2);
// const fetchingRate = ref(false);
// const lastFetchTime = ref(localStorage.getItem('omg_rate_fetch_time') || '');
// const rateSource = ref('ExchangeRate-API');

// 批量更新QC状态
const batchUpdatingQC = ref(false);

// 全量同步状态
const syncingProducts = ref(false);

// 选中商品同步状态
const syncingSelectedProducts = ref(false);

// 更新主图库状态
const updatingMainImages = ref(false);

// 查看主图相关状态
const showMainImagesDialog = ref(false);
const loadingMainImages = ref(false);
const mainImagesList = ref([]);
const previewImageList = ref([]);
const settingMainImage = ref(false);
const cancelingMainImage = ref(false);
const currentProductSku = ref('');

// 汇率相关函数已注释
/*
async function fetchRealTimeExchangeRate() {
  fetchingRate.value = true;
  try {
    // 使用免费的汇率API - ExchangeRate-API
    const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');

    if (!response.ok) {
      throw new Error('汇率API请求失败');
    }

    const data = await response.json();
    const usdToCnyRate = data.rates.CNY;

    if (usdToCnyRate && usdToCnyRate > 0) {
      exchangeRate.value = parseFloat(usdToCnyRate.toFixed(2));
      tempExchangeRate.value = exchangeRate.value;

      // 保存到本地存储
      localStorage.setItem('omg_exchange_rate', exchangeRate.value.toString());
      localStorage.setItem('omg_rate_fetch_time', new Date().toLocaleString('zh-CN'));
      lastFetchTime.value = new Date().toLocaleString('zh-CN');

      proxy.$modal.msgSuccess(`实时汇率获取成功！当前汇率：1 USD = ${exchangeRate.value} CNY`);
    } else {
      throw new Error('汇率数据格式错误');
    }
  } catch (error) {
    console.error('获取实时汇率失败:', error);

    // 降级处理：尝试备用API
    try {
      const backupResponse = await fetch('https://open.er-api.com/v6/latest/USD');
      const backupData = await backupResponse.json();

      if (backupData.rates && backupData.rates.CNY) {
        const backupRate = backupData.rates.CNY;
        exchangeRate.value = parseFloat(backupRate.toFixed(2));
        tempExchangeRate.value = exchangeRate.value;

        localStorage.setItem('omg_exchange_rate', exchangeRate.value.toString());
        localStorage.setItem('omg_rate_fetch_time', new Date().toLocaleString('zh-CN'));
        lastFetchTime.value = new Date().toLocaleString('zh-CN');
        rateSource.value = 'Open Exchange Rates';

        proxy.$modal.msgSuccess(`实时汇率获取成功！当前汇率：1 USD = ${exchangeRate.value} CNY`);
      } else {
        throw new Error('备用API也无法获取汇率');
      }
    } catch (backupError) {
      console.error('备用汇率API也失败:', backupError);
      proxy.$modal.msgError('获取实时汇率失败，请检查网络连接或手动设置汇率。当前使用默认汇率：' + exchangeRate.value);
    }
  } finally {
    fetchingRate.value = false;
  }
}

async function autoFetchExchangeRate() {
  const lastFetch = localStorage.getItem('omg_rate_fetch_time');
  const today = new Date().toDateString();
  const lastFetchDate = lastFetch ? new Date(lastFetch).toDateString() : '';

  // 如果今天还没有获取过汇率，自动获取
  if (lastFetchDate !== today) {
    console.log('自动获取今日汇率...');
    await fetchRealTimeExchangeRate();
  } else {
    console.log('今日汇率已获取，使用缓存汇率:', exchangeRate.value);
    lastFetchTime.value = lastFetch;
  }
}
*/

/**
 * 批量更新所有商品QC
 */
async function handleBatchUpdateQC() {
  try {
    // 确认操作
    await proxy.$modal.confirm('确认要批量获取/更新所有商品的QC信息吗？此操作可能需要较长时间，请耐心等待。');

    batchUpdatingQC.value = true;

    const response = await batchUpdateAllQC();

    // 更新成功后刷新列表
    getList();
    proxy.$modal.msgSuccess('批量更新QC成功！');

  } catch (error) {
    console.error('批量更新QC失败:', error);
    if (error !== 'cancel') { // 用户取消操作时不显示错误信息
      proxy.$modal.msgError('批量更新QC失败，请稍后重试');
    }
  } finally {
    batchUpdatingQC.value = false;
  }
}

/**
 * 全量同步重量/运输天数
 */
async function handleSyncAllProducts() {
  try {
    // 确认操作
    await proxy.$modal.confirm('确认要全量同步所有商品的重量和运输天数吗？此操作可能需要较长时间，请耐心等待。');

    syncingProducts.value = true;

    const response = await syncAllProductsWeightAndShipping();

    // 更新成功后刷新列表
    getList();

    // 根据返回的数据显示详细的同步结果
    console.log('全量同步返回数据:', response);

    // 根据您提供的返回格式，数据应该直接在response中
    // 格式: { "msg": "全量同步完成", "total": 4, "code": 200, "success": 1, "failed": 3 }
    if (response && (response.total !== undefined || response.success !== undefined || response.failed !== undefined)) {
      const { total, success, failed, msg } = response;
      const successMsg = `${msg || '同步完成'}！总数：${total || 0}，成功：${success || 0}，失败：${failed || 0}`;
      proxy.$modal.msgSuccess(successMsg);
    } else if (response && response.data && (response.data.total !== undefined || response.data.success !== undefined || response.data.failed !== undefined)) {
      // 如果数据在response.data中
      const { total, success, failed, msg } = response.data;
      const successMsg = `${msg || '同步完成'}！总数：${total || 0}，成功：${success || 0}，失败：${failed || 0}`;
      proxy.$modal.msgSuccess(successMsg);
    } else {
      // 如果没有详细数据，显示默认消息
      const defaultMsg = (response && response.msg) || (response && response.data && response.data.msg) || '全量同步重量/运输天数完成！';
      proxy.$modal.msgSuccess(defaultMsg);
    }

  } catch (error) {
    console.error('全量同步失败:', error);
    if (error !== 'cancel') { // 用户取消操作时不显示错误信息
      proxy.$modal.msgError('全量同步失败，请稍后重试');
    }
  } finally {
    syncingProducts.value = false;
  }
}

/**
 * 同步选中商品
 */
async function handleSyncSelectedProducts() {
  try {
    if (ids.value.length === 0) {
      proxy.$modal.msgWarning('请先选择要同步的商品');
      return;
    }

    // 获取选中商品的SKU
    const selectedProducts = OmgProductsList.value.filter(product =>
      ids.value.includes(product.productId)
    );

    const skuList = selectedProducts.map(product => product.sku).filter(sku => sku);

    if (skuList.length === 0) {
      proxy.$modal.msgError('选中的商品中没有有效的SKU，无法进行同步');
      return;
    }

    // 确认操作
    await proxy.$modal.confirm(
      `确认要同步选中的 ${skuList.length} 个商品吗？\n\nSKU列表：\n${skuList.join(', ')}`,
      '同步选中商品确认',
      {
        confirmButtonText: '确认同步',
        cancelButtonText: '取消',
        type: 'info'
      }
    );

    syncingSelectedProducts.value = true;

    // 将SKU数组转换为字符串（用逗号分隔）
    const skusString = skuList.join(',');

    const response = await syncProductsBySkus(skusString);

    // 更新成功后刷新列表
    getList();

    // 根据返回的数据显示结果
    console.log('选中商品同步返回数据:', response);

    if (response && (response.total !== undefined || response.success !== undefined || response.failed !== undefined)) {
      const { total, success, failed, msg } = response;
      const successMsg = `${msg || '同步完成'}！总数：${total || skuList.length}，成功：${success || 0}，失败：${failed || 0}`;
      proxy.$modal.msgSuccess(successMsg);
    } else if (response && response.data && (response.data.total !== undefined || response.data.success !== undefined || response.data.failed !== undefined)) {
      // 如果数据在response.data中
      const { total, success, failed, msg } = response.data;
      const successMsg = `${msg || '同步完成'}！总数：${total || skuList.length}，成功：${success || 0}，失败：${failed || 0}`;
      proxy.$modal.msgSuccess(successMsg);
    } else {
      // 如果没有详细数据，显示默认消息
      const defaultMsg = (response && response.msg) || (response && response.data && response.data.msg) || `选中商品同步完成！共处理 ${skuList.length} 个商品`;
      proxy.$modal.msgSuccess(defaultMsg);
    }

  } catch (error) {
    console.error('选中商品同步失败:', error);
    if (error !== 'cancel') { // 用户取消操作时不显示错误信息
      proxy.$modal.msgError('选中商品同步失败，请稍后重试');
    }
  } finally {
    syncingSelectedProducts.value = false;
  }
}

/**
 * 更新所有商品主图库
 */
async function handleUpdateAllMainImages() {
  try {
    // 确认操作
    await proxy.$modal.confirm('确认要更新所有商品的主图库吗？此操作可能需要较长时间，请耐心等待。');

    updatingMainImages.value = true;

    const response = await updateAllProductMainImages();

    // 更新成功后刷新列表
    getList();

    // 根据返回的数据显示结果
    console.log('更新主图库返回数据:', response);

    if (response && (response.total !== undefined || response.success !== undefined || response.failed !== undefined)) {
      const { total, success, failed, msg } = response;
      const successMsg = `${msg || '更新完成'}！总数：${total || 0}，成功：${success || 0}，失败：${failed || 0}`;
      proxy.$modal.msgSuccess(successMsg);
    } else if (response && response.data && (response.data.total !== undefined || response.data.success !== undefined || response.data.failed !== undefined)) {
      // 如果数据在response.data中
      const { total, success, failed, msg } = response.data;
      const successMsg = `${msg || '更新完成'}！总数：${total || 0}，成功：${success || 0}，失败：${failed || 0}`;
      proxy.$modal.msgSuccess(successMsg);
    } else {
      // 如果没有详细数据，显示默认消息
      const defaultMsg = (response && response.msg) || (response && response.data && response.data.msg) || '更新主图库完成！';
      proxy.$modal.msgSuccess(defaultMsg);
    }

  } catch (error) {
    console.error('更新主图库失败:', error);
    if (error !== 'cancel') { // 用户取消操作时不显示错误信息
      proxy.$modal.msgError('更新主图库失败，请稍后重试');
    }
  } finally {
    updatingMainImages.value = false;
  }
}

/**
 * 查看商品所有主图
 */
async function handleViewMainImages(row) {
  try {
    if (!row.sku) {
      proxy.$modal.msgError('商品SKU不存在，无法查看主图');
      return;
    }

    currentProductSku.value = row.sku; // 保存当前商品SKU
    loadingMainImages.value = true;
    showMainImagesDialog.value = true;

    const response = await getProductMainImagesBySku(row.sku);

    console.log('获取主图返回数据:', response);

    if (response && response.data && Array.isArray(response.data)) {
      // 按displayOrder排序
      mainImagesList.value = response.data.sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0));
      // 构建预览图片列表
      previewImageList.value = mainImagesList.value.map(item => item.ossImageUrl);
    } else {
      mainImagesList.value = [];
      previewImageList.value = [];
      proxy.$modal.msgWarning(response?.msg || '暂无主图数据');
    }

  } catch (error) {
    console.error('获取主图失败:', error);
    proxy.$modal.msgError('获取主图失败，请稍后重试');
    mainImagesList.value = [];
    previewImageList.value = [];
  } finally {
    loadingMainImages.value = false;
  }
}

/**
 * 设置主图
 */
async function handleSetMainImage(image) {
  try {
    await proxy.$modal.confirm(`确认将"${image.imageName}"设置为主图吗？`);

    settingMainImage.value = true;

    const data = {
      sku: currentProductSku.value,
      imageId: image.id,
      imageName: image.imageName,
      ossImageUrl: image.ossImageUrl
    };

    const response = await setProductMainImage(data);

    if (response && response.code === 200) {
      proxy.$modal.msgSuccess('设置主图成功');
      // 重新获取主图列表
      await handleViewMainImages({ sku: currentProductSku.value });
      // 刷新商品列表
      getList();
    } else {
      proxy.$modal.msgError(response?.msg || '设置主图失败');
    }

  } catch (error) {
    console.error('设置主图失败:', error);
    if (error !== 'cancel') { // 用户取消操作时不显示错误信息
      proxy.$modal.msgError('设置主图失败，请稍后重试');
    }
  } finally {
    settingMainImage.value = false;
  }
}

/**
 * 取消主图
 */
async function handleCancelMainImage(image) {
  try {
    await proxy.$modal.confirm(`确认取消"${image.imageName}"的主图设置吗？取消后该商品将没有主图。`, '取消主图确认', {
      confirmButtonText: '确认取消',
      cancelButtonText: '保持主图',
      type: 'warning'
    });

    cancelingMainImage.value = true;

    const data = {
      sku: currentProductSku.value,
      imageId: image.id,
      imageName: image.imageName
    };

    const response = await cancelProductMainImage(data);

    if (response && response.code === 200) {
      proxy.$modal.msgSuccess('取消主图成功');
      // 重新获取主图列表
      await handleViewMainImages({ sku: currentProductSku.value });
      // 刷新商品列表
      getList();
    } else {
      proxy.$modal.msgError(response?.msg || '取消主图失败');
    }

  } catch (error) {
    console.error('取消主图失败:', error);
    if (error !== 'cancel') { // 用户取消操作时不显示错误信息
      proxy.$modal.msgError('取消主图失败，请稍后重试');
    }
  } finally {
    cancelingMainImage.value = false;
  }
}

/**
 * 检测单个商品状态
 */
async function handleCheckProductStatus(row) {
  let loadingMessage = null;

  try {
    // 验证必要字段
    if (!row.sku) {
      proxy.$modal.msgError('商品SKU不存在，无法检测状态');
      return;
    }

    if (!row.platform) {
      proxy.$modal.msgError('商品平台信息不存在，无法检测状态');
      return;
    }

    // 设置当前行的加载状态
    row.checking = true;

    // 显示检测中的提示
    loadingMessage = proxy.$message({
      message: `正在检测商品状态：${row.name || row.sku}`,
      type: 'info',
      duration: 0, // 不自动关闭
      showClose: false,
      customClass: 'checking-message'
    });

    const response = await checkSingleProductStatus(row.sku, row.platform);

    // 关闭检测中的提示
    if (loadingMessage) {
      loadingMessage.close();
      loadingMessage = null;
    }

    if (response.code === 200) {
      const result = response.data;
      const productExists = result.productExists;
      const statusText = productExists ? '商品存在' : '商品不存在';
      const statusType = productExists ? 'success' : 'warning';
      const statusDescription = productExists ? '商品状态正常，可以正常销售' : '商品已下架或不存在，建议检查商品链接';

      // 显示检测成功提示
      proxy.$message.success('检测完成！');

      // 显示检测结果
      proxy.$modal.alert(
        `商品名称：${row.name || '未知'}\n商品SKU：${result.sku || row.sku}\n所属平台：${getPlatformText(result.platform || row.platform)}\n检测结果：${statusText}\n状态说明：${statusDescription}\n检查成功：${result.success ? '是' : '否'}`,
        '商品状态检测结果',
        {
          confirmButtonText: '确定',
          type: statusType
        }
      );

      // 如果商品不存在，可以考虑更新商品状态
      if (!productExists) {
        proxy.$modal.confirm('检测到商品已下架，是否将商品状态设为下架？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 调用下架方法
          handleInactive(row);
        }).catch(() => {
          // 用户取消，不做任何操作
        });
      }
    } else {
      proxy.$modal.msgError(response.msg || '检测失败');
    }
  } catch (error) {
    console.error('检测商品状态失败:', error);
    proxy.$modal.msgError('检测失败：' + error.message);
  } finally {
    // 清除加载状态
    row.checking = false;

    // 确保提示消息被关闭
    if (loadingMessage) {
      try {
        loadingMessage.close();
      } catch (e) {
        // 忽略关闭提示时的错误
        console.warn('关闭检测提示时出错:', e);
      }
    }
  }
}

// 汇率转换函数已注释
/*
function rmbToUsd(rmbAmount) {
  if (!rmbAmount || isNaN(rmbAmount)) return 0;
  return (parseFloat(rmbAmount) / exchangeRate.value).toFixed(2);
}

function usdToRmb(usdAmount) {
  if (!usdAmount || isNaN(usdAmount)) return 0;
  return (parseFloat(usdAmount) * exchangeRate.value).toFixed(2);
}
*/

// 临时替代函数，直接返回原值（不进行汇率转换）
function rmbToUsd(rmbAmount) {
  if (!rmbAmount || isNaN(rmbAmount)) return 0;
  return parseFloat(rmbAmount).toFixed(2);
}

function usdToRmb(usdAmount) {
  if (!usdAmount || isNaN(usdAmount)) return 0;
  return parseFloat(usdAmount).toFixed(2);
}

const OmgProductsList = ref([]);
const brandOptions = ref([]); // 品牌选项列表
const brandLoading = ref(false); // 品牌加载状态
const categoryOptions = ref([]); // 分类选项列表
const categoryLoading = ref(false); // 分类加载状态
const tagInputValue = ref([]); // 标签输入值
const tagOptions = ref(JSON.parse(localStorage.getItem('omg_tag_options')) || ['热销', '新品', '推荐', '限时', '特价', '精选']); // 预设标签选项
const tagTypes = ['', 'success', 'warning', 'danger', 'info']; // 标签颜色类型

// 标签管理相关变量
const showTagManageDialog = ref(false);
const showEditTagDialog = ref(false);
const tagLoading = ref(false);
const tagList = ref([]);
const tagTotal = ref(0);
const editTagForm = ref({ tagName: '' });
const tagQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  tagName: ''
});
let currentTagId = null;

const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const showSummerDialog = ref(false);
const summerProducts = ref([]);
const summerSelectedIds = ref([]);
const summerActiveTab = ref('selected');
const summerProductsList = ref([]);
// 分页相关
const summerProductsLoading = ref(false);
const summerProductsHasMore = ref(true);
const summerProductsPage = ref(1);
const summerProductsPageSize = ref(300);
// 预加载状态
const summerProductsPreloading = ref(false);
const summerProductsPreloadProgress = ref({ current: 0, total: 0 });
const importOpen = ref(false);
const uploadFile = ref(null);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    slug: null,
    minPrice: null,
    maxPrice: null,
    merchant: null,
    platform: null,
    status: null,
    productStatus: null,
    productAttributes: null
  },
  rules: {
    name: [
      { required: true, message: "商品名称不能为空", trigger: "blur" }
    ],
    // slug: [
    //   { required: true, message: "商品标识符不能为空", trigger: "blur" }
    // ],
   // description: [
   //   { required: true, message: "商品描述不能为空", trigger: "blur" }
   // ],
    brandId: [
      { required: true, message: "商品品牌不能为空", trigger: "change" }
    ],
    categoryId: [
      { required: true, message: "商品分类不能为空", trigger: "change" }
    ],
    price: [
      { required: true, message: "商品价格不能为空", trigger: "blur" }
    ],
    originalPrice: [
      { required: true, message: "商品原价不能为空", trigger: "blur" }
    ],
    // mainImage: [
    //   { required: true, message: "商品主图链接不能为空", trigger: "blur" }
    // ],
    sku: [
      { required: true, message: "商品SKU编号不能为空", trigger: "blur" }
    ],
    stock: [
      { required: false, message: "商品库存不能为空", trigger: "blur" }
    ],
    likes: [
      { required: false, message: "商品点赞数不能为空", trigger: "blur" }
    ],
    views: [
      { required: false, message: "商品浏览量不能为空", trigger: "blur" }
    ],
    rating: [
      { required: false, message: "商品评分不能为空", trigger: "blur" }
    ],
    totalRatings: [
      { required: false, message: "评分总数不能为空", trigger: "blur" }
    ],
    status: [
      { required: true, message: "商品状态不能为空", trigger: "change" }
    ],
    qcImages: [
      { required: false, message: "商品QC图可选填", trigger: "blur" }
    ],
    merchant: [
      { required: false, message: "所属商家不能为空", trigger: "blur" }
    ],
    platform: [
      { required: true, message: "所属平台不能为空", trigger: "change" }
    ]
  },
  //表格展示列
  columns: [
    { key: 0, label: '商品id', visible: true },
    { key: 1, label: '商品名称', visible: true },
    { key: 2, label: '商品标识', visible: false },
    { key: 3, label: '商品描述', visible: false },
    { key: 4, label: '商品价格', visible: true },
    { key: 5, label: '商品原价', visible: true },
    { key: 6, label: '商品主图', visible: true },
    { key: 7, label: 'SKU编号', visible: true },
    { key: 8, label: 'QC', visible: true },
    { key: 9, label: '重量(g)', visible: true },
    { key: 10, label: '运输天数', visible: true },
    { key: 11, label: '点赞数', visible: true },
    { key: 12, label: '浏览量', visible: true },
    { key: 13, label: '虚拟浏览量', visible: true },
    { key: 14, label: '所属商家', visible: true },
    { key: 15, label: '所属平台', visible: true },
    { key: 16, label: '商品状态', visible: true },
    { key: 17, label: '创建时间', visible: true },
    { key: 18, label: '标签', visible: true },
    { key: 19, label: '来源链接', visible: true },
    { key: 20, label: '推荐状态', visible: true },
    { key: 21, label: '商品视频', visible: true },
    { key: 22, label: '商品属性', visible: true }
  ],
});

const { queryParams, form, rules, columns } = toRefs(data);

/** 查询omg_商品列表 */
function getList() {
  loading.value = true;
  listOmgProducts(queryParams.value).then(response => {
    OmgProductsList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 页面初始化时预加载夏季新品相关数据 */
function initSummerProductsData() {
  // 延迟1秒后开始预加载，避免影响主要数据的加载
  setTimeout(() => {
    console.log('开始预加载夏季新品商品数据...');
    preloadProductsForSummer();
  }, 1000);
}

/** 查询分类列表 */
function getCategoryList() {
  categoryLoading.value = true;
  listOmgCategories({
    pageSize: 999 // 获取所有分类
  }).then(response => {
    categoryOptions.value = response.rows || [];
  }).finally(() => {
    categoryLoading.value = false;
  });
}

/** 查询品牌列表 */
function getBrandList() {
  brandLoading.value = true;
  listOmgBrands({
    pageSize: 999 // 获取所有品牌
  }).then(response => {
    brandOptions.value = response.rows || [];
  }).finally(() => {
    brandLoading.value = false;
  });
}

/** 上架按钮操作 */
function handleActive(row) {
  proxy.$modal.confirm('是否确认上架商品"' + row.name + '"？').then(() => {
    return updateProductStatus(row.productId, 'active');
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("上架成功");
  }).catch(() => {});
}
/** 下架按钮操作 */
function handleInactive(row) {
  proxy.$modal.confirm('是否确认下架商品"' + row.name + '"？').then(() => {
    return updateProductStatus(row.productId, 'inactive');
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("下架成功");
  }).catch(() => {});
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    productId: null,
    sellerId: null,
    categoryId: null,
    brandId: null,
    name: null,
    slug: null,
    description: null,
    price: null,
    originalPrice: null,
    mainImage: null,
    qcImages: null,
    video: null,
    sku: null,
    stock: null,
    likes: null,
    views: null,
    virtualViews: null,
    rating: null,
    totalRatings: null,
    merchant: null,
    platform: null,
    status: null,
    createdAt: null,
    updatedAt: null,
    tag: null,
    fromUrl: null,
    productStatus: 0,
    qc: null,
    weight: null,
    averageArrival: null,
    productAttributes: null
  };
  tagInputValue.value = []; // 清空标签输入
  proxy.resetForm("OmgProductsRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  // 验证价格区间
  if (queryParams.value.minPrice && queryParams.value.maxPrice) {
    const minPrice = parseFloat(queryParams.value.minPrice);
    const maxPrice = parseFloat(queryParams.value.maxPrice);

    if (minPrice > maxPrice) {
      proxy.$modal.msgError("最低价格不能大于最高价格");
      return;
    }
  }

  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  // 手动清除价格区间，确保重置功能完整
  queryParams.value.minPrice = null;
  queryParams.value.maxPrice = null;
  handleQuery();
}

/** 验证价格区间 */
function validatePriceRange() {
  if (queryParams.value.minPrice && queryParams.value.maxPrice) {
    const minPrice = parseFloat(queryParams.value.minPrice);
    const maxPrice = parseFloat(queryParams.value.maxPrice);

    if (minPrice > maxPrice) {
      proxy.$modal.msgWarning("最低价格不能大于最高价格");
    }
  }
}

/** 清除价格区间 */
function clearPriceRange() {
  queryParams.value.minPrice = null;
  queryParams.value.maxPrice = null;
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.productId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  // 只加载分类列表，不加载品牌列表
  getCategoryList();
  // 清空品牌列表
  brandOptions.value = [];
  open.value = true;
  title.value = "添加商品";
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  const _productId = row.productId || ids.value;
  // 先获取品牌列表和分类列表
  await getCategoryList();
  await getBrandList();

  console.log("11brandOptions",brandOptions);
  console.log("11brandOptions.value",brandOptions.value);
  // 然后获取商品详情
  getOmgProducts(_productId).then(response => {
    const data = response.data;
    
    // 处理qcImages格式，将对象数组转换为逗号分隔的URL字符串，以适应image-upload组件
    if (data.qcImages && Array.isArray(data.qcImages)) {
      data.qcImages = data.qcImages.map(item => item.imageUrl).join(',');
    }
    
    // 将数据库中的美元价格转换为人民币显示
    const formData = {
      ...data,
      brandId: data.omgBrand?.brandId || null,
      price: data.price ? usdToRmb(data.price) : null,
      originalPrice: data.originalPrice ? usdToRmb(data.originalPrice) : null
    };
    
    form.value = formData;

    // 处理标签数据，将JSON字符串转为数组
    if (data.tag) {
      try {
        const parsedTags = parseTags(data.tag);
        tagInputValue.value = parsedTags;
      } catch (e) {
        // 如果解析失败，将整个字符串作为一个标签
        tagInputValue.value = [data.tag];
      }
    } else {
      tagInputValue.value = [];
    }

    // 如果有分类ID，根据分类ID获取品牌列表
    if (data.categoryId) {
      brandLoading.value = true;
      getBrandListByCategoryId(data.categoryId).then(res => {
        brandOptions.value = res.data || [];
        console.log("编辑时根据分类ID获取的品牌列表:", brandOptions.value);
        
        // 如果当前商品的品牌不在获取的列表中，将其添加到列表头部
        if (data.omgBrand && data.omgBrand.brandId && 
            !brandOptions.value.some(brand => brand.brandId === data.omgBrand.brandId)) {
          brandOptions.value.unshift(data.omgBrand);
        }
      }).finally(() => {
        brandLoading.value = false;
      });
    }

    // 编辑商品时（回显）
    if (data.mainImage) {
      if (typeof data.mainImage === 'string') {
        // 如果是单张图片字符串，转为数组
        data.mainImage = [data.mainImage];
      } else if (!Array.isArray(data.mainImage)) {
        data.mainImage = [];
      }
    }

    open.value = true;
    title.value = "修改商品";
  });
}

/** 处理标签变更 */
function handleTagChange(value) {
  // 转换为JSON字符串
  if (Array.isArray(value) && value.length > 0) {
    form.value.tag = JSON.stringify(value);
  } else {
    form.value.tag = null;
  }
}

/** 解析标签 */
function parseTags(tagString) {
  if (!tagString) return [];
  
  try {
    // 尝试解析JSON
    const parsed = JSON.parse(tagString);
    if (Array.isArray(parsed)) {
      return parsed;
    }
    // 如果解析结果不是数组，则将其作为单个标签返回
    return [String(parsed)];
  } catch (e) {
    // 如果解析失败，则将整个字符串作为单个标签返回
    return [tagString];
  }
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["OmgProductsRef"].validate(valid => {
    
    if (valid) {
      // 创建表单数据的副本
      const formData = { ...form.value };
      
      // 处理标签，将标签数组转换为JSON字符串
      if (tagInputValue.value && tagInputValue.value.length > 0) {
        formData.tag = JSON.stringify(tagInputValue.value);
      } else {
        formData.tag = null;
      }
      
      // 将人民币价格转换为美元存储到数据库
      if (formData.price) {
        formData.price = rmbToUsd(formData.price);
      }
      if (formData.originalPrice) {
        formData.originalPrice = rmbToUsd(formData.originalPrice);
      }
      
      // 处理QC图片为对象数组格式
      if (formData.qcImages) {
        // 从字符串中分离出图片URL数组
        const imageUrls = formData.qcImages.split(',').filter(url => url.trim());
        
        // 转换为指定的对象数组格式
        formData.qcImages = imageUrls.map(imageUrl => ({
          imageUrl,
          displayOrder: 0
        }));
      } else {
        formData.qcImages = [];
      }

      // 处理视频字段，确保是字符串而非数组
      if (formData.video && Array.isArray(formData.video)) {
        // 如果是数组，取第一个元素作为字符串
        formData.video = formData.video[0] || "";
      }

      // 提交时，保证 mainImage 为字符串（逗号分隔）
      if (Array.isArray(formData.mainImage)) {
        formData.mainImage = formData.mainImage.join(',');
      } else if (!formData.mainImage) {
        formData.mainImage = '';
      }

      if (form.value.productId != null) {
        updateOmgProducts(formData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addOmgProducts(formData).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _productIds = row.productId || ids.value;
  proxy.$modal.confirm('是否确认删除omg_商品编号为"' + _productIds + '"的数据项？').then(function () {
    return delOmgProducts(_productIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/OmgProducts/export', {
    ...queryParams.value
  }, `OmgProducts_${new Date().getTime()}.xlsx`)
}

/** 跳转到商品状态检查页面 */
function handleGoToStatusCheck() {
  // 跳转到正式的商品状态检查页面
  proxy.$router.push('/system/productStatusCheck/index')
}

/** 获取状态对应的类名 */
function getStatusClass(status) {
  const statusMap = {
    active: 'status-success',
    inactive: 'status-warning',
    out_of_stock: 'status-danger',
    draft: 'status-info'
  };
  return statusMap[status] || 'status-default';
}

/** 获取状态对应的文字 */
function getStatusText(status) {
  const statusTextMap = {
    active: '正常',
    inactive: '下架',
    out_of_stock: '缺货',
    draft: '草稿'
  };
  return statusTextMap[status] || status;
}

/** 获取推荐状态对应的文字 */
function getProductStatusText(status) {
  const statusTextMap = {
    0: '普通商品',
    1: '热销',
    2: '推荐'
  };
  return statusTextMap[status] || '未知状态';
}

/** 获取推荐状态对应的标签类型 */
function getProductStatusTagType(status) {
  const statusTypeMap = {
    0: 'info',
    1: 'danger',
    2: 'success'
  };
  return statusTypeMap[status] || '';
}

/** 处理设置推荐状态 */
function handleSetProductStatus(row, status) {
  const statusTextMap = {
    0: '普通商品',
    1: '热销',
    2: '推荐'
  };
  proxy.$modal.confirm(`确认将商品"${row.name}"设置为"${statusTextMap[status]}"状态吗？`).then(() => {
    // 使用已有的修改接口更新商品推荐状态
    return updateOmgProducts({
      productId: row.productId,
      productStatus: status
    });
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("设置成功");
  }).catch(() => {});
}

/** 品牌选择改变时的处理 */
function handleBrandChange(brandId) {
  form.value.brandId = brandId;
  const selectedBrand = brandOptions.value.find(item => item.brandId === brandId);
  if (selectedBrand) {
    form.value.omgBrand = {
      brandId: selectedBrand.brandId,
      name: selectedBrand.name,
      logoUrl: selectedBrand.logoUrl
    };
  } else {
    form.value.omgBrand = null;
  }
}

/** 分类选择改变时的处理 */
function handleCategoryChange(categoryId) {
  form.value.categoryId = categoryId;
  const selectedCategory = categoryOptions.value.find(item => item.categoryId === categoryId);
  if (selectedCategory) {
    form.value.category = {
      categoryId: selectedCategory.categoryId,
      name: selectedCategory.name
    };
    
    // 清空当前品牌选择
    form.value.brandId = null;
    form.value.omgBrand = null;
    
    // 根据分类ID获取品牌列表
    brandLoading.value = true;
    getBrandListByCategoryId(categoryId).then(response => {
      brandOptions.value = response.data || [];
      console.log("根据分类ID获取的品牌列表:", brandOptions.value);
    }).finally(() => {
      brandLoading.value = false;
    });
  } else {
    form.value.category = null;
    // 清空品牌列表
    brandOptions.value = [];
  }
}

/** 获取平台对应的文字 */
function getPlatformText(platform) {
  const platformTextMap = {
    taobao: '淘宝',
    weidian: '微店',
    '1688': '1688'
  };
  return platformTextMap[platform] || platform;
}

// 获取所有商品列表（当季新品用）- 支持分页，每页300条
function getAllProductsForSummer(reset = true, silent = false) {
  if (summerProductsLoading.value) return;

  if (reset) {
    summerProducts.value = [];
    summerProductsPage.value = 1;
    summerProductsHasMore.value = true;
  }

  if (!summerProductsHasMore.value) return;

  summerProductsLoading.value = true;

  // 使用新的 getAll 接口
  getAllOmgProducts({
    pageNum: summerProductsPage.value,
    pageSize: 300  // 固定每页300条
  }).then(res => {
    console.log('🔍 新接口返回数据:', res);
    // 根据新接口返回结构，数据在 list 字段中
    const rawList = res.data?.list || res.list || [];
    console.log('📋 提取的商品列表:', rawList);
    const newProducts = rawList.map(item => ({
      ...item,
      productId: String(item.productId || item.product_id || item.id), // 统一为字符串
      product_name: item.product_name || item.name || item.title // 统一为 product_name
    }));
    console.log('🔄 处理后的商品数据:', newProducts);

    if (reset) {
      summerProducts.value = newProducts;
    } else {
      // 追加新数据到现有列表
      summerProducts.value = [...summerProducts.value, ...newProducts];
    }

    // 判断是否还有更多数据：如果返回的数据少于300条，说明没有更多了
    summerProductsHasMore.value = newProducts.length === 300;

    // 只有在成功加载数据后才增加页码
    if (newProducts.length > 0) {
      summerProductsPage.value++;
    }

    if (!silent) {
      console.log(`已加载第${summerProductsPage.value - 1}页，共${newProducts.length}条商品，总计${summerProducts.value.length}条`);
    }

  }).catch(error => {
    console.error('加载商品列表失败:', error);
    if (!silent && proxy && proxy.$modal && proxy.$modal.msgError) {
      proxy.$modal.msgError('加载商品列表失败');
    }
  }).finally(() => {
    summerProductsLoading.value = false;
  });
}

// 预加载商品数据（页面加载时静默执行）- 加载所有数据
async function preloadProductsForSummer() {
  console.log('🚀 开始预加载所有夏季新品商品数据...');

  // 设置预加载状态
  summerProductsPreloading.value = true;
  summerProductsPreloadProgress.value = { current: 0, total: 0 };

  // 重置状态
  summerProducts.value = [];
  summerProductsPage.value = 1;
  summerProductsHasMore.value = true;

  let totalLoaded = 0;
  let currentPage = 1;

  try {
    while (summerProductsHasMore.value) {
      console.log(`📦 正在加载第${currentPage}页商品数据...`);

      const res = await getAllOmgProducts({
        pageNum: currentPage,
        pageSize: 300  // 每页300条，减少请求次数
      });

      console.log(`🔍 预加载第${currentPage}页数据:`, res);
      // 使用新接口的数据结构，数据在 list 字段中
      const rawList = res.data?.list || res.list || [];
      console.log(`📋 第${currentPage}页商品列表:`, rawList);
      const newProducts = rawList.map(item => ({
        ...item,
        productId: String(item.productId || item.product_id || item.id),
        product_name: item.product_name || item.name || item.title
      }));
      console.log(`🔄 第${currentPage}页处理后数据:`, newProducts);

      // 追加新数据
      summerProducts.value = [...summerProducts.value, ...newProducts];
      totalLoaded += newProducts.length;

      // 更新进度 - 适配新接口的数据结构
      summerProductsPreloadProgress.value = {
        current: totalLoaded,
        total: res.data?.total || res.total || totalLoaded
      };

      // 判断是否还有更多数据
      summerProductsHasMore.value = newProducts.length === 300;
      currentPage++;

      console.log(`✅ 第${currentPage - 1}页加载完成，本页${newProducts.length}条，累计${totalLoaded}条`);

      // 如果没有更多数据，退出循环
      if (!summerProductsHasMore.value) {
        break;
      }

      // 添加小延迟，避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    // 更新最终状态
    summerProductsPage.value = currentPage;
    console.log(`🎉 所有商品数据预加载完成！总计${totalLoaded}条商品`);

  } catch (error) {
    console.error('❌ 预加载商品数据失败:', error);
    // 预加载失败时重置状态，让用户打开弹窗时重新加载
    summerProducts.value = [];
    summerProductsPage.value = 1;
    summerProductsHasMore.value = true;
  } finally {
    // 结束预加载状态
    summerProductsPreloading.value = false;
  }
}

// 获取夏季新品列表
function getSummerProductsList() {
  listOmgSummerProducts({ pageNum: 1, pageSize: 15 }).then(res => {
    summerProductsList.value = res.rows || [];
  });
}



watch(showSummerDialog, (val) => {
  if (val) {
    getSummerProductsList(); // 获取夏季新品列表

    // 如果还没有商品数据，则开始加载
    if (summerProducts.value.length === 0) {
      console.log('弹窗打开时商品数据为空，开始加载...');
      getAllProductsForSummer(true, false); // 获取所有商品列表，重置分页
    } else {
      console.log(`弹窗打开时已有${summerProducts.value.length}条商品数据，无需重新加载`);
    }

    summerActiveTab.value = 'selected'; // 默认显示已选商品标签页
  }
});

// 计算夏季新品表的 productId 列表
const summerProductsTableIds = computed(() => summerProductsList.value.map(item => item.productId || item.product_id));

// 计算可选择的剩余数量
const remainingSelectLimit = computed(() => {
  const maxTotal = 14; // 总共最多14个
  const currentSelected = summerProductsList.value.length; // 已选择的商品数量
  return Math.max(0, maxTotal - currentSelected);
});

// 计算选择提示文字
const selectHintText = computed(() => {
  const current = summerProductsList.value.length;
  const remaining = remainingSelectLimit.value;
  if (current === 0) {
    return `选择商品（最多14个）`;
  } else if (remaining === 0) {
    return `已达到最大数量（${current}/14）`;
  } else {
    return `选择商品（已选${current}个，还可选${remaining}个）`;
  }
});

// 判断当前选择和表中是否一致
const isSummerSelectionChanged = computed(() => {
  const a = summerSelectedIds.value.slice().sort().join(',');
  const b = summerProductsTableIds.value.slice().sort().join(',');
  return a !== b;
});

function handleAddOrUpdateSummerProducts() {
  // 新增：选中但不在表中的商品
  const toAdd = summerSelectedIds.value.filter(id => !summerProductsTableIds.value.includes(id));
  // 替换：顺序变化或内容变化
  const updateList = summerProductsList.value.map((item, idx) => {
    const newId = summerSelectedIds.value[idx];
    if (!newId) return null;
    const newProduct = summerProducts.value.find(p => p.productId === newId);
    if (!newProduct) return null;
    // 只在id变化时才update
    if (item.productId !== newId) {
      return {
        ...item,
        productId: newProduct.productId,
        productName: newProduct.product_name || newProduct.name || newProduct.title || '',
        mainImage: newProduct.mainImage,
        price: newProduct.price,
        likes: newProduct.likes,
        views: newProduct.views,
        comments: newProduct.comments || newProduct.commentCount || 0,
        favorites: newProduct.favorites || newProduct.favoriteCount || 0,
        imageUrl: newProduct.mainImage,
        stock: newProduct.stock,
        status: newProduct.status,
        createdAt: newProduct.createdAt,
        platform: newProduct.platform,
      };
    }
    return null;
  }).filter(Boolean);
  const addList = summerProducts.value.filter(item => toAdd.includes(item.productId)).map(item => ({
    productId: item.productId,
    productName: item.product_name || item.name || item.title || '',
    mainImage: item.mainImage,
    price: item.price,
    likes: item.likes,
    views: item.views,
    comments: item.comments || item.commentCount || 0,
    favorites: item.favorites || item.favoriteCount || 0,
    imageUrl: item.mainImage,
    stock: item.stock,
    status: item.status,
    createdAt: item.createdAt,
    platform: item.platform,
  }));
  Promise.all([
    ...updateList.map(data => updateOmgSummerProducts(data)),
    ...addList.map(data => addOmgSummerProducts(data))
  ]).then(() => {
    if (proxy && proxy.$modal && proxy.$modal.msgSuccess) {
      proxy.$modal.msgSuccess('操作成功');
    }
    summerActiveTab.value = 'selected';
    summerSelectedIds.value = [];
    getSummerProductsList();
  });
}





// 合并夏季新品和所有商品，去重并统一格式
const summerProductOptions = computed(() => {
  const map = new Map();
  summerProductsList.value.forEach(item => {
    const id = String(item.productId || item.product_id);
    map.set(id, {
      ...item,
      productId: id,
      product_name: item.productName || item.product_name || item.name || item.title || '未命名商品',
      mainImage: item.mainImage || item.imageUrl
    });
  });
  summerProducts.value.forEach(item => {
    const id = String(item.productId || item.product_id || item.id);
    if (!map.has(id)) {
      map.set(id, {
        ...item,
        productId: id,
        product_name: item.product_name || item.name || item.title || '未命名商品',
        mainImage: item.mainImage || item.imageUrl
      });
    }
  });
  return Array.from(map.values());
});



// 右侧商品信息直接用 summerProductOptions 查找
const summerSelectedProducts = computed(() => {
  return summerSelectedIds.value
    .map(id => summerProductOptions.value.find(item => String(item.productId) === String(id)))
    .filter(Boolean);
});

function handleClearSummerProducts() {
  if (!summerProductsList.value.length) return;
  proxy.$modal.confirm('确定要清空所有夏季新品吗？').then(() => {
    const ids = summerProductsList.value.map(item => item.id);
    // 逐个删除
    Promise.all(ids.map(id => delOmgSummerProducts(id)))
      .then(() => {
        proxy.$modal.msgSuccess('已清空');
        getSummerProductsList();
      });
  });
}

// 处理选择变更，验证数量限制
function handleSummerSelectChange(selectedIds) {
  const currentSelected = summerProductsList.value.length;
  const maxTotal = 14;
  const maxNewSelections = maxTotal - currentSelected;

  // 如果选择的数量超过了剩余可选数量，进行截取
  if (selectedIds.length > maxNewSelections) {
    const truncatedIds = selectedIds.slice(0, maxNewSelections);
    summerSelectedIds.value = truncatedIds;

    if (maxNewSelections === 0) {
      proxy.$modal.msgWarning('已达到最大选择数量（14个），无法再添加商品');
    } else {
      proxy.$modal.msgWarning(`最多只能再选择${maxNewSelections}个商品，已自动调整选择`);
    }
  }
}

// 处理下拉框显示/隐藏
function handleSelectVisibleChange(visible) {
  if (visible) {
    // 下拉框打开时，绑定滚动事件
    nextTick(() => {
      const selectDropdown = document.querySelector('.summer-product-select-popper .el-select-dropdown__wrap');
      if (selectDropdown) {
        selectDropdown.addEventListener('scroll', handleSelectScroll);
      }
    });
  } else {
    // 下拉框关闭时，移除滚动事件
    const selectDropdown = document.querySelector('.summer-product-select-popper .el-select-dropdown__wrap');
    if (selectDropdown) {
      selectDropdown.removeEventListener('scroll', handleSelectScroll);
    }
  }
}

// 处理下拉框滚动事件
function handleSelectScroll(event) {
  const { scrollTop, scrollHeight, clientHeight } = event.target;
  // 当滚动到底部附近时加载更多（提前50px触发）
  // 只有在还有更多数据且不在加载中时才触发
  if (scrollTop + clientHeight >= scrollHeight - 50 && summerProductsHasMore.value && !summerProductsLoading.value) {
    console.log('触发滚动加载更多商品...');
    getAllProductsForSummer(false); // false表示不重置，追加数据
  }
}

function handleImport() {
  importOpen.value = true;
  uploadFile.value = null;
}

function importTemplate() {
  proxy.download("system/OmgProducts/importTemplate", {}, 
    `OmgProducts_template_${new Date().getTime()}.xlsx`);
}

function cancelImport() {
  importOpen.value = false;
  uploadFile.value = null;
}

function handleFileChange(file) {
  uploadFile.value = file.raw;
}

function submitImport() {
  if (!uploadFile.value) {
    proxy.$modal.msgError("请选择要上传的文件");
    return;
  }
  proxy.$modal.loading("正在导入数据，请稍候...");
  importOmgProducts(uploadFile.value).then(response => {
    proxy.$modal.msgSuccess(response.msg);
    importOpen.value = false;
    getList();
  }).catch(error => {
    console.error("导入失败:", error);
  }).finally(() => {
    proxy.$modal.closeLoading();
  });
}

// 汇率相关逻辑已注释
/*
const showExchangeRateDialog = ref(false);
const tempExchangeRate = ref(exchangeRate.value);

function saveExchangeRate() {
  exchangeRate.value = tempExchangeRate.value;
  localStorage.setItem('omg_exchange_rate', exchangeRate.value.toString());
  showExchangeRateDialog.value = false;
  proxy.$modal.msgSuccess("汇率设置成功");
}

function cancelExchangeRate() {
  showExchangeRateDialog.value = false;
  tempExchangeRate.value = exchangeRate.value; // 重置为当前汇率
}

// 监听汇率设置对话框的打开状态
watch(showExchangeRateDialog, (val) => {
  if (val) {
    tempExchangeRate.value = exchangeRate.value; // 显示当前汇率
  }
});
*/

// 监听标签管理对话框的打开状态
watch(showTagManageDialog, (val) => {
  if (val) {
    getTagList(); // 获取标签列表
  }
});

function getProductDisplayName(item) {
  if (!item) return '';
  
  // 获取商品名称，优先使用productName，然后是product_name，最后是name或title
  const productName = item.productName || item.product_name || item.name || item.title || '未命名商品';
  
  // 获取SKU，如果没有SKU则只返回商品名称
  const sku = item.sku;
  
  if (sku && sku.trim()) {
    return `${productName} - ${sku}`;
  } else {
    return productName;
  }
}

/** 获取标签列表 */
function getTagList() {
  tagLoading.value = true;
  // 这里我们直接使用本地存储的标签，实际项目中可能需要从后端获取
  const storedTags = tagOptions.value;
  const filteredTags = storedTags
    .filter(tag => tag.toLowerCase().includes((tagQueryParams.value.tagName || '').toLowerCase()))
    .map((tag, index) => ({
      tagId: index + 1,
      tagName: tag,
      createdAt: new Date().toISOString()
    }));
    
  // 模拟分页
  const start = (tagQueryParams.value.pageNum - 1) * tagQueryParams.value.pageSize;
  const end = start + tagQueryParams.value.pageSize;
  tagList.value = filteredTags.slice(start, end);
  tagTotal.value = filteredTags.length;
  tagLoading.value = false;
}

/** 标签搜索 */
function handleTagQuery() {
  tagQueryParams.value.pageNum = 1;
  getTagList();
}

/** 重置标签搜索 */
function resetTagQuery() {
  tagQueryParams.value = {
    pageNum: 1,
    pageSize: 10,
    tagName: ''
  };
  handleTagQuery();
}

/** 打开编辑标签对话框 */
function openEditTag(tag) {
  editTagForm.value = { tagName: tag.tagName };
  currentTagId = tag.tagId;
  showEditTagDialog.value = true;
}

/** 新增标签 */
function handleAddTag() {
  editTagForm.value = { tagName: '' };
  currentTagId = null;
  showEditTagDialog.value = true;
}

/** 提交编辑标签 */
function submitEditTag() {
  if (!editTagForm.value.tagName.trim()) {
    proxy.$modal.msgError("标签名称不能为空");
    return;
  }
  
  const newTags = [...tagOptions.value];
  
  if (currentTagId !== null) {
    // 编辑现有标签
    const index = currentTagId - 1;
    if (index >= 0 && index < newTags.length) {
      newTags[index] = editTagForm.value.tagName;
    }
  } else {
    // 新增标签
    if (!newTags.includes(editTagForm.value.tagName)) {
      newTags.push(editTagForm.value.tagName);
    } else {
      proxy.$modal.msgError("标签名称已存在");
      return;
    }
  }
  
  // 更新本地存储和显示
  localStorage.setItem('omg_tag_options', JSON.stringify(newTags));
  tagOptions.value = newTags;
  
  showEditTagDialog.value = false;
  getTagList();
  proxy.$modal.msgSuccess("保存成功");
}

/** 删除标签 */
function handleDeleteTag(tagId) {
  const index = tagId - 1;
  if (index < 0 || index >= tagOptions.value.length) return;
  
  proxy.$modal.confirm('确定要删除该标签吗？').then(() => {
    const newTags = [...tagOptions.value];
    newTags.splice(index, 1);
    
    // 更新本地存储和显示
    localStorage.setItem('omg_tag_options', JSON.stringify(newTags));
    tagOptions.value = newTags;
    
    getTagList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

getList();
getCategoryList(); // Load categories when the page opens

// 页面加载时的初始化（汇率获取已注释）
onMounted(() => {
  // autoFetchExchangeRate(); // 已注释
  initSummerProductsData(); // 预加载夏季新品商品数据
});

/** 预览视频 */
  function previewVideo(videoUrl) {
    if (!videoUrl) return;
    
    // 使用Element Plus的MessageBox显示视频
    proxy.$msgbox({
      title: '视频预览',
      message: h('div', { class: 'video-preview-container' }, [
        h('video', {
          controls: true,
          style: 'width: 100%; max-height: 500px;',
        }, [
          h('source', {
            src: videoUrl,
            type: 'video/mp4'
          }),
          '您的浏览器不支持视频播放'
        ])
      ]),
      showCancelButton: false,
      confirmButtonText: '关闭'
    });
  }
</script>

<style>
.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 5px;
}

.import-tips {
  margin-bottom: 15px;
  padding: 8px 16px;
  background-color: #f8f8f9;
  border-radius: 4px;
  border-left: 5px solid #ff9900;
}

.import-warning {
  font-weight: bold;
  color: #ff9900;
  margin-bottom: 8px;
}

.import-tips ul {
  padding-left: 20px;
  margin: 5px 0;
}

.import-tips li {
  line-height: 1.8;
}

.status-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-success {
  background-color: #67C23A;
}

.status-warning {
  background-color: #E6A23C;
}

.status-danger {
  background-color: #F56C6C;
}

.status-info {
  background-color: #909399;
}

.status-default {
  background-color: #DCDFE6;
}

.selected-product-info {
  display: flex;
  gap: 20px;
  padding: 15px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  background-color: #F8F9FA;
}

.product-details {
  flex: 1;
}

.product-details p {
  margin: 8px 0;
  line-height: 1.5;
}

.product-details strong {
  color: #606266;
  margin-right: 5px;
}

.selected-products-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.selected-product-item {
  width: calc(50% - 10px);
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  background-color: #F8F9FA;
}

.selected-product-item .product-details {
  margin-left: 10px;
}

.selected-product-item .product-name {
  font-weight: bold;
  margin-bottom: 5px;
  color: #303133;
}

.selected-product-item .product-price {
  color: #F56C6C;
  font-weight: bold;
}

.summer-products-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.summer-product-item {
  width: calc(33.33% - 10px);
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  background-color: #F8F9FA;
}

@media (max-width: 1200px) {
  .summer-product-item {
    width: calc(50% - 10px);
  }
}

@media (max-width: 768px) {
  .selected-product-item {
    width: 100%;
  }
  .summer-product-item {
    width: 100%;
  }
}

.price-tip {
  margin-top: 4px;
  color: #909399;
  line-height: 1.2;
}

.price-tip small {
  font-size: 12px;
  color: #E6A23C;
  background-color: #fdf6ec;
  padding: 2px 8px;
  border-radius: 3px;
  border-left: 3px solid #E6A23C;
}

/* 汇率相关样式已注释 */
/*
.exchange-rate-tip {
  margin-top: 4px;
  color: #909399;
  line-height: 1.2;
}

.exchange-rate-desc {
  padding: 8px 16px;
  background-color: #f8f8f9;
  border-radius: 4px;
  border-left: 5px solid #ff9900;
}

.realtime-rate-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.rate-info {
  padding: 10px;
  background-color: #f0f9ff;
  border: 1px solid #3b82f6;
  border-radius: 4px;
  margin-top: 10px;
}

.rate-info p {
  margin: 5px 0;
}

.rate-source {
  color: #6b7280;
  font-style: italic;
}
*/

/* 商品状态检测提示样式 */
:deep(.checking-message) {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

:deep(.checking-message .el-message__icon) {
  color: #1890ff;
}

:deep(.checking-message .el-message__content) {
  font-weight: 500;
}

/* 标签相关样式 */
.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  justify-content: center;
}

.tag-input.el-select {
  width: 100%;
}

.tag-input-container {
  display: flex;
  gap: 10px;
  align-items: flex-start;
}

.tag-input-container .el-select {
  flex: 1;
}

.el-tag {
  margin: 2px;
}

/* 视频预览相关样式 */
.video-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;
}

.video-container video {
  max-width: 100%;
  max-height: 100%;
}

/* 主图查看对话框样式 */
.main-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 16px 0;
}

.image-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.image-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.image-wrapper {
  position: relative;
}

.thumbnail-image {
  width: 100%;
  height: 150px;
  cursor: pointer;
}

.image-info {
  padding: 12px;
  background-color: #fff;
}

.image-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.image-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.image-type {
  font-size: 12px;
  color: #909399;
}

.image-actions {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.main-image-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

/* 价格区间样式 */
.price-range-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.price-separator {
  color: #909399;
  font-weight: 500;
  margin: 0 4px;
}

.price-unit {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
  margin-left: 4px;
}

.price-range-container .el-input {
  flex-shrink: 0;
}

.price-range-container .el-button {
  flex-shrink: 0;
  font-size: 12px;
}

@media (max-width: 768px) {
  .price-range-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .price-separator {
    display: none;
  }
}

/* 夏季新品选择器样式 */
.summer-product-select-popper .el-select-dropdown {
  max-height: 400px;
}

.summer-product-select-popper .el-select-dropdown__wrap {
  max-height: 400px;
}
</style>