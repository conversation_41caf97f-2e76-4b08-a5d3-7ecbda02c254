import request from '@/utils/request'

// 查询omg_商品列表
export function listOmgProducts(query) {
  return request({
    url: '/system/OmgProducts/list',
    method: 'get',
    params: query,
    timeout: 300000 // 300秒超时（5分钟），适应大数据量查询
  })
}

// 获取所有商品列表（用于当季新品选择）
export function getAllOmgProducts(query) {
  return request({
    url: '/system/OmgProducts/getAll',
    method: 'get',
    params: query,
    timeout: 30000 // 30秒超时
  })
}

// 查询omg_商品详细
export function getOmgProducts(productId) {
  return request({
    url: '/system/OmgProducts/' + productId,
    method: 'get'
  })
}

// 新增omg_商品
export function addOmgProducts(data) {
  return request({
    url: '/system/OmgProducts',
    method: 'post',
    data: data
  })
}

// 修改商品状态
export function updateProductStatus(productId, status) {
  return request({
    url: `/system/OmgProducts/status`,
    method: 'put',
    data: { productId, status }
  });
}

// 修改omg_商品
export function updateOmgProducts(data) {
  return request({
    url: '/system/OmgProducts',
    method: 'put',
    data: data
  })
}

// 删除omg_商品
export function delOmgProducts(productId) {
  return request({
    url: '/system/OmgProducts/' + productId,
    method: 'delete'
  })
}

// 批量导入omg_商品
export function importOmgProducts(file) {
  const formData = new FormData();
  formData.append('file', file);
  return request({
    url: '/system/OmgProducts/importData',
    method: 'post',
    data: formData,
    headers: { 'Content-Type': 'multipart/form-data' }
  });
}

// 批量更新所有商品QC
export function batchUpdateAllQC() {
  return request({
    url: '/front/omg/products/qc-update/batch-all',
    method: 'post',
    timeout: 60000 // 60秒超时，因为批量更新可能需要较长时间
  });
}

// 全量同步重量/运输天数
export function syncAllProductsWeightAndShipping() {
  return request({
    url: '/system/cnfans-sync/products/all',
    method: 'post',
    timeout: 600000 // 600秒超时（10分钟），全量同步需要较长时间
  });
}

// 更新所有商品主图库
export function updateAllProductMainImages() {
  return request({
    url: '/system/OmgProducts/updateAllProductMainImages',
    method: 'post',
    timeout: 600000 // 600秒超时（10分钟），更新主图库需要较长时间
  });
}

// 根据SKU获取商品所有主图
export function getProductMainImagesBySku(sku) {
  return request({
    url: `/system/OmgProducts/updateMainImagesBySku/${sku}`,
    method: 'post',
    timeout: 30000 // 30秒超时
  });
}

// 设置商品主图
export function setProductMainImage(data) {
  return request({
    url: '/system/OmgProducts/setMainImage',
    method: 'post',
    data: data,
    timeout: 30000 // 30秒超时
  });
}

// 取消商品主图
export function cancelProductMainImage(data) {
  return request({
    url: '/system/OmgProducts/cancelMainImage',
    method: 'post',
    data: data,
    timeout: 30000 // 30秒超时
  });
}

// 根据SKU字符串同步商品
export function syncProductsBySkus(skus) {
  return request({
    url: '/system/cnfans-sync/products/sync-by-skus',
    method: 'post',
    data: { skus: skus },
    timeout: 300000 // 300秒超时（5分钟），批量同步需要较长时间
  });
}