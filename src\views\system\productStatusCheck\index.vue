<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>商品状态检查管理</h2>
      <p class="page-description">定期检查商品在各个平台（1688、淘宝、微店）的实际状态，自动更新下架商品</p>
    </div>

    <!-- 功能卡片区域 -->
    <el-row :gutter="20" class="mb20">
      <!-- 定时任务配置卡片 -->
      <el-col :span="12">
        <el-card class="box-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span><el-icon><Timer /></el-icon> 定时任务配置</span>
              <el-switch
                v-model="scheduleConfig.enabled"
                @change="handleToggleSchedule"
                active-text="启用"
                inactive-text="禁用"
                :loading="toggleLoading"
                v-hasPermi="['system:productStatusCheck:toggle']"
              />
            </div>
          </template>
          <div class="schedule-info">
            <div v-if="scheduleConfig.enabled" class="schedule-details">
              <p><strong>执行时间：</strong>每月{{ scheduleConfig.dayOfMonth }}号 {{ String(scheduleConfig.hour).padStart(2, '0') }}:{{ String(scheduleConfig.minute).padStart(2, '0') }}</p>
              <p><strong>任务描述：</strong>{{ scheduleConfig.description || '定期执行商品状态检查' }}</p>
              <p><strong>下次执行：</strong>{{ getNextExecutionTime() }}</p>
            </div>
            <div v-else class="schedule-disabled">
              <p class="text-muted">定时任务未启用</p>
            </div>
            <div class="schedule-actions">
              <el-button 
                type="primary" 
                size="small" 
                @click="showScheduleDialog = true"
                v-hasPermi="['system:productStatusCheck:config']"
              >
                <el-icon><Setting /></el-icon> 配置任务
              </el-button>
              <el-button 
                type="danger" 
                size="small" 
                @click="handleRemoveSchedule"
                :disabled="!scheduleConfig.enabled"
                v-hasPermi="['system:productStatusCheck:remove']"
              >
                <el-icon><Delete /></el-icon> 删除任务
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 手动检查卡片 -->
      <el-col :span="12">
        <el-card class="box-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span><el-icon><Search /></el-icon> 手动检查</span>
            </div>
          </template>
          <div class="manual-check">
            <div class="check-options">
              <el-button 
                type="success" 
                @click="handleExecuteFullCheck"
                :loading="fullCheckLoading"
                v-hasPermi="['system:productStatusCheck:execute']"
              >
                <el-icon><Refresh /></el-icon> 全量检查
              </el-button>
              <el-button 
                type="primary" 
                @click="showPageCheckDialog = true"
                v-hasPermi="['system:productStatusCheck:page']"
              >
                <el-icon><Document /></el-icon> 分页检查
              </el-button>
              <el-button 
                type="warning" 
                @click="showSingleCheckDialog = true"
                v-hasPermi="['system:productStatusCheck:single']"
              >
                <el-icon><View /></el-icon> 单个检查
              </el-button>
            </div>
            <div class="check-tips">
              <el-alert
                title="检查说明"
                type="info"
                :closable="false"
                show-icon
              >
                <template #default>
                  <ul>
                    <li>全量检查：检查所有有SKU和平台信息的商品</li>
                    <li>分页检查：按页检查商品，适合大量商品的分批处理</li>
                    <li>单个检查：检查指定SKU的商品状态</li>
                  </ul>
                </template>
              </el-alert>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 检查结果展示 -->
    <el-card v-if="lastCheckResult" class="result-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span><el-icon><DataAnalysis /></el-icon> 最近检查结果</span>
          <el-tag :type="getResultTagType(lastCheckResult.status)">
            {{ getResultStatusText(lastCheckResult.status) }}
          </el-tag>
        </div>
      </template>
      <div class="result-content">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="result-item">
              <div class="result-number">{{ lastCheckResult.totalChecked || 0 }}</div>
              <div class="result-label">检查总数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="result-item">
              <div class="result-number success">{{ lastCheckResult.activeCount || 0 }}</div>
              <div class="result-label">检查成功</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="result-item">
              <div class="result-number warning">{{ lastCheckResult.inactiveCount || 0 }}</div>
              <div class="result-label">商品下架</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="result-item">
              <div class="result-number danger">{{ lastCheckResult.errorCount || 0 }}</div>
              <div class="result-label">检查失败</div>
            </div>
          </el-col>
        </el-row>
        <div class="result-time">
          <span>检查时间：{{ formatTime(lastCheckResult.checkTime) }}</span>
          <span>耗时：{{ lastCheckResult.duration || '未知' }}</span>
        </div>
      </div>
    </el-card>

    <!-- 定时任务配置对话框 -->
    <el-dialog
      v-model="showScheduleDialog"
      title="配置定时任务"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="scheduleForm" :rules="scheduleRules" ref="scheduleFormRef" label-width="100px">
        <el-form-item label="执行日期" prop="dayOfMonth">
          <el-select v-model="scheduleForm.dayOfMonth" placeholder="请选择每月执行日期">
            <el-option
              v-for="day in 28"
              :key="day"
              :label="`每月${day}号`"
              :value="day"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="执行时间" prop="time">
          <el-time-picker
            v-model="scheduleTime"
            format="HH:mm"
            placeholder="选择执行时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="scheduleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述"
          />
        </el-form-item>
        <el-form-item label="启用状态" prop="enabled">
          <el-switch v-model="scheduleForm.enabled" active-text="启用" inactive-text="禁用" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showScheduleDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSaveSchedule" :loading="saveScheduleLoading">
            保存配置
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 分页检查对话框 -->
    <el-dialog
      v-model="showPageCheckDialog"
      title="分页检查商品状态"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form :model="pageCheckForm" :rules="pageCheckRules" ref="pageCheckFormRef" label-width="100px">
        <el-form-item label="每页数量" prop="pageSize">
          <el-input-number
            v-model="pageCheckForm.pageSize"
            :min="1"
            :max="1000"
            controls-position="right"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="页码" prop="pageNum">
          <el-input-number
            v-model="pageCheckForm.pageNum"
            :min="1"
            controls-position="right"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showPageCheckDialog = false">取消</el-button>
          <el-button type="primary" @click="handleExecutePageCheck" :loading="pageCheckLoading">
            开始检查
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 单个检查对话框 -->
    <el-dialog
      v-model="showSingleCheckDialog"
      title="检查单个商品状态"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form :model="singleCheckForm" :rules="singleCheckRules" ref="singleCheckFormRef" label-width="100px">
        <el-form-item label="商品SKU" prop="sku">
          <el-input v-model="singleCheckForm.sku" placeholder="请输入商品SKU" />
        </el-form-item>
        <el-form-item label="所属平台" prop="platform">
          <el-select v-model="singleCheckForm.platform" placeholder="请选择平台" style="width: 100%">
            <el-option label="1688" value="1688" />
            <el-option label="淘宝" value="taobao" />
            <el-option label="微店" value="weidian" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showSingleCheckDialog = false">取消</el-button>
          <el-button type="primary" @click="handleExecuteSingleCheck" :loading="singleCheckLoading">
            检查状态
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ProductStatusCheck">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Timer, Setting, Delete, Search, Refresh, Document, View, DataAnalysis } from '@element-plus/icons-vue'
import {
  executeProductStatusCheck,
  checkSingleProductStatus,
  executePageProductStatusCheck,
  configSchedule,
  getScheduleConfig,
  toggleSchedule,
  removeSchedule
} from '@/api/system/productStatusCheck'

const { proxy } = getCurrentInstance()

// 表单引用
const scheduleFormRef = ref()
const pageCheckFormRef = ref()
const singleCheckFormRef = ref()

// 响应式数据
const showScheduleDialog = ref(false)
const showPageCheckDialog = ref(false)
const showSingleCheckDialog = ref(false)
const toggleLoading = ref(false)
const fullCheckLoading = ref(false)
const pageCheckLoading = ref(false)
const singleCheckLoading = ref(false)
const saveScheduleLoading = ref(false)

// 定时任务配置
const scheduleConfig = reactive({
  dayOfMonth: 15,
  hour: 2,
  minute: 30,
  enabled: false,
  description: ''
})

// 定时任务表单
const scheduleForm = reactive({
  dayOfMonth: 15,
  hour: 2,
  minute: 30,
  enabled: true,
  description: '每月定期执行商品状态检查'
})

const scheduleTime = ref(new Date(2024, 0, 1, 2, 30))

// 分页检查表单
const pageCheckForm = reactive({
  pageSize: 100,
  pageNum: 1
})

// 单个检查表单
const singleCheckForm = reactive({
  sku: '',
  platform: ''
})

// 最近检查结果
const lastCheckResult = ref(null)

// 表单验证规则
const scheduleRules = {
  dayOfMonth: [
    { required: true, message: '请选择执行日期', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入任务描述', trigger: 'blur' }
  ]
}

const pageCheckRules = {
  pageSize: [
    { required: true, message: '请输入每页数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 1000, message: '每页数量必须在1-1000之间', trigger: 'blur' }
  ],
  pageNum: [
    { required: true, message: '请输入页码', trigger: 'blur' },
    { type: 'number', min: 1, message: '页码必须大于0', trigger: 'blur' }
  ]
}

const singleCheckRules = {
  sku: [
    { required: true, message: '请输入商品SKU', trigger: 'blur' }
  ],
  platform: [
    { required: true, message: '请选择平台', trigger: 'change' }
  ]
}

// 组件挂载时加载配置
onMounted(() => {
  loadScheduleConfig()
})

/** 加载定时任务配置 */
async function loadScheduleConfig() {
  try {
    const response = await getScheduleConfig()
    if (response.code === 200 && response.data) {
      Object.assign(scheduleConfig, response.data)
      Object.assign(scheduleForm, response.data)

      // 设置时间选择器的值
      scheduleTime.value = new Date(2024, 0, 1, scheduleConfig.hour, scheduleConfig.minute)
    }
  } catch (error) {
    console.error('加载定时任务配置失败:', error)
  }
}

/** 切换定时任务状态 */
async function handleToggleSchedule(enabled) {
  toggleLoading.value = true
  try {
    const response = await toggleSchedule(enabled)
    if (response.code === 200) {
      scheduleConfig.enabled = enabled
      ElMessage.success(enabled ? '定时任务已启用' : '定时任务已禁用')
    } else {
      // 恢复开关状态
      scheduleConfig.enabled = !enabled
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    // 恢复开关状态
    scheduleConfig.enabled = !enabled
    ElMessage.error('操作失败：' + error.message)
  } finally {
    toggleLoading.value = false
  }
}

/** 保存定时任务配置 */
async function handleSaveSchedule() {
  if (!scheduleFormRef.value) return

  try {
    await scheduleFormRef.value.validate()

    saveScheduleLoading.value = true

    // 从时间选择器获取小时和分钟
    const time = scheduleTime.value
    scheduleForm.hour = time.getHours()
    scheduleForm.minute = time.getMinutes()

    const response = await configSchedule(scheduleForm)
    if (response.code === 200) {
      Object.assign(scheduleConfig, scheduleForm)
      showScheduleDialog.value = false
      ElMessage.success('定时任务配置保存成功')
    } else {
      ElMessage.error(response.msg || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败：' + error.message)
  } finally {
    saveScheduleLoading.value = false
  }
}

/** 删除定时任务 */
async function handleRemoveSchedule() {
  try {
    await ElMessageBox.confirm('确认删除定时任务吗？删除后需要重新配置。', '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await removeSchedule()
    if (response.code === 200) {
      scheduleConfig.enabled = false
      ElMessage.success('定时任务删除成功')
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + error.message)
    }
  }
}

/** 执行全量检查 */
async function handleExecuteFullCheck() {
  try {
    await ElMessageBox.confirm('全量检查可能需要较长时间，确认执行吗？', '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    fullCheckLoading.value = true
    const startTime = Date.now()

    const response = await executeProductStatusCheck()
    if (response.code === 200) {
      const endTime = Date.now()
      const duration = Math.round((endTime - startTime) / 1000) + '秒'
      const result = response.data

      // 根据实际API返回的数据结构适配
      lastCheckResult.value = {
        status: result.success ? 'success' : 'error',
        totalChecked: result.totalCount || 0,
        activeCount: result.successCount || 0,  // 成功检查到的商品数量
        inactiveCount: result.offlineCount || 0, // 下架商品数量
        errorCount: result.failCount || 0,      // 检查失败数量
        checkTime: new Date(),
        duration: duration
      }

      ElMessage.success(response.msg || '全量检查完成')
    } else {
      ElMessage.error(response.msg || '检查失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('检查失败：' + error.message)
    }
  } finally {
    fullCheckLoading.value = false
  }
}

/** 执行分页检查 */
async function handleExecutePageCheck() {
  if (!pageCheckFormRef.value) return

  try {
    await pageCheckFormRef.value.validate()

    pageCheckLoading.value = true
    const startTime = Date.now()

    const response = await executePageProductStatusCheck(
      pageCheckForm.pageSize,
      pageCheckForm.pageNum
    )

    if (response.code === 200) {
      const endTime = Date.now()
      const duration = Math.round((endTime - startTime) / 1000) + '秒'
      const result = response.data

      // 根据实际API返回的数据结构适配
      lastCheckResult.value = {
        status: result.success ? 'success' : 'error',
        totalChecked: result.totalCount || 0,
        activeCount: result.successCount || 0,  // 成功检查到的商品数量
        inactiveCount: result.offlineCount || 0, // 下架商品数量
        errorCount: result.failCount || 0,      // 检查失败数量
        checkTime: new Date(),
        duration: duration
      }

      showPageCheckDialog.value = false
      ElMessage.success(response.msg || `第${pageCheckForm.pageNum}页检查完成`)
    } else {
      ElMessage.error(response.msg || '检查失败')
    }
  } catch (error) {
    ElMessage.error('检查失败：' + error.message)
  } finally {
    pageCheckLoading.value = false
  }
}

/** 执行单个检查 */
async function handleExecuteSingleCheck() {
  if (!singleCheckFormRef.value) return

  try {
    await singleCheckFormRef.value.validate()

    singleCheckLoading.value = true

    const response = await checkSingleProductStatus(
      singleCheckForm.sku,
      singleCheckForm.platform
    )

    if (response.code === 200) {
      const result = response.data
      showSingleCheckDialog.value = false

      // 根据实际返回的数据结构适配
      const productExists = result.productExists
      const statusText = productExists ? '商品存在' : '商品不存在'
      const statusType = productExists ? 'success' : 'warning'
      const statusDescription = productExists ? '商品状态正常' : '商品已下架或不存在'

      ElMessageBox.alert(
        `商品SKU: ${result.sku || singleCheckForm.sku}\n平台: ${getPlatformText(result.platform || singleCheckForm.platform)}\n检查结果: ${statusText}\n状态说明: ${statusDescription}\n检查成功: ${result.success ? '是' : '否'}`,
        '检查结果',
        {
          confirmButtonText: '确定',
          type: statusType
        }
      )

      // 重置表单
      singleCheckForm.sku = ''
      singleCheckForm.platform = ''
    } else {
      ElMessage.error(response.msg || '检查失败')
    }
  } catch (error) {
    ElMessage.error('检查失败：' + error.message)
  } finally {
    singleCheckLoading.value = false
  }
}

/** 获取下次执行时间 */
function getNextExecutionTime() {
  if (!scheduleConfig.enabled) return '未启用'

  const now = new Date()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth()

  // 计算下次执行时间
  let nextExecution = new Date(currentYear, currentMonth, scheduleConfig.dayOfMonth, scheduleConfig.hour, scheduleConfig.minute)

  // 如果当前时间已过本月执行时间，则计算下月
  if (nextExecution <= now) {
    nextExecution = new Date(currentYear, currentMonth + 1, scheduleConfig.dayOfMonth, scheduleConfig.hour, scheduleConfig.minute)
  }

  return formatTime(nextExecution)
}

/** 获取结果标签类型 */
function getResultTagType(status) {
  const typeMap = {
    success: 'success',
    warning: 'warning',
    error: 'danger'
  }
  return typeMap[status] || 'info'
}

/** 获取结果状态文字 */
function getResultStatusText(status) {
  const textMap = {
    success: '检查成功',
    warning: '部分失败',
    error: '检查失败'
  }
  return textMap[status] || '未知状态'
}

/** 获取平台文字 */
function getPlatformText(platform) {
  const platformMap = {
    '1688': '1688',
    'taobao': '淘宝',
    'weidian': '微店'
  }
  return platformMap[platform] || platform
}

/** 格式化时间 */
function formatTime(time) {
  if (!time) return ''
  const date = new Date(time)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
  padding: 20px 0;
  border-bottom: 1px solid #e4e7ed;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.box-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.card-header span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.schedule-info {
  min-height: 120px;
}

.schedule-details p {
  margin: 8px 0;
  color: #606266;
  line-height: 1.6;
}

.schedule-disabled {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80px;
}

.text-muted {
  color: #909399;
  font-style: italic;
}

.schedule-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.manual-check {
  min-height: 120px;
}

.check-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.check-options .el-button {
  justify-content: flex-start;
}

.check-tips {
  margin-top: 15px;
}

.check-tips ul {
  margin: 0;
  padding-left: 20px;
}

.check-tips li {
  margin: 4px 0;
  color: #606266;
  font-size: 13px;
}

.result-card {
  margin-top: 20px;
}

.result-content {
  padding: 10px 0;
}

.result-item {
  text-align: center;
  padding: 10px;
}

.result-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.result-number.success {
  color: #67c23a;
}

.result-number.warning {
  color: #e6a23c;
}

.result-number.danger {
  color: #f56c6c;
}

.result-label {
  font-size: 14px;
  color: #909399;
}

.result-time {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  color: #909399;
  font-size: 13px;
}

.dialog-footer {
  text-align: right;
}

.mb20 {
  margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .check-options {
    flex-direction: column;
  }

  .schedule-actions {
    flex-direction: column;
  }

  .result-time {
    flex-direction: column;
    gap: 5px;
  }
}
</style>
